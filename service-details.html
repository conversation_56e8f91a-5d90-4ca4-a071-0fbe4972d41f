<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务详情 - PDF分割工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e1e8ed;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 25px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f3f7;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2rem;
            color: white;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: #5a6c7d;
            min-width: 100px;
        }

        .info-value {
            color: #2c3e50;
            font-weight: 400;
            text-align: right;
            flex: 1;
        }

        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status.running {
            background-color: #d4edda;
            color: #155724;
        }

        .status.stopped {
            background-color: #f8d7da;
            color: #721c24;
        }

        .dependency-list {
            list-style: none;
        }

        .dependency-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f3f7;
        }

        .dependency-item:last-child {
            border-bottom: none;
        }

        .dependency-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .dependency-version {
            color: #6c757d;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background-color: #f8f9fa;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .config-item {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
        }

        .config-key {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .config-value {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            color: #6f42c1;
            font-size: 0.9rem;
        }

        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .contact-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            opacity: 0.7;
        }

        .repo-link {
            color: #0366d6;
            text-decoration: none;
            font-weight: 500;
        }

        .repo-link:hover {
            text-decoration: underline;
        }

        .metric {
            text-align: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #495057;
            display: block;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>PDF分割工具服务</h1>
            <p>基于Web的PDF分割服务，支持自动识别竖线并分割PDF文档</p>
        </div>

        <!-- 第一行：负责人信息和业务信息 -->
        <div class="grid">
            <!-- 负责人信息 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background-color: #3498db;">👤</div>
                    <h2 class="card-title">负责人信息</h2>
                </div>
                <div class="contact-info">
                    <span class="contact-icon">📧</span>
                    <span><EMAIL></span>
                </div>
                <div class="contact-info">
                    <span class="contact-icon">📱</span>
                    <span>+86 138-0000-0000</span>
                </div>
                <div class="contact-info">
                    <span class="contact-icon">👨‍💻</span>
                    <span>张三 (主要开发者)</span>
                </div>
                <div class="contact-info">
                    <span class="contact-icon">🏢</span>
                    <span>技术部门</span>
                </div>
                <div class="contact-info">
                    <span class="contact-icon">⏰</span>
                    <span>工作时间: 9:00-18:00</span>
                </div>
            </div>

            <!-- 业务信息 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background-color: #e74c3c;">📋</div>
                    <h2 class="card-title">业务信息</h2>
                </div>
                <div class="info-item">
                    <span class="info-label">服务名称</span>
                    <span class="info-value">PDF分割工具</span>
                </div>
                <div class="info-item">
                    <span class="info-label">业务类型</span>
                    <span class="info-value">文档处理服务</span>
                </div>
                <div class="info-item">
                    <span class="info-label">主要功能</span>
                    <span class="info-value">PDF文档智能分割</span>
                </div>
                <div class="info-item">
                    <span class="info-label">服务等级</span>
                    <span class="info-value">生产级</span>
                </div>
                <div class="info-item">
                    <span class="info-label">业务描述</span>
                    <span class="info-value">自动识别PDF中的竖线并进行分割，支持试卷等文档处理</span>
                </div>
            </div>
        </div>

        <!-- 第二行：仓库信息和配置信息 -->
        <div class="grid">
            <!-- 仓库信息 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background-color: #9b59b6;">🔗</div>
                    <h2 class="card-title">仓库信息</h2>
                </div>
                <div class="info-item">
                    <span class="info-label">仓库地址</span>
                    <span class="info-value">
                        <a href="https://github.com/example/pdf-splitter" class="repo-link">
                            github.com/example/pdf-splitter
                        </a>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">分支</span>
                    <span class="info-value">main</span>
                </div>
                <div class="info-item">
                    <span class="info-label">最新提交</span>
                    <span class="info-value">2024-01-15 14:30:25</span>
                </div>
                <div class="info-item">
                    <span class="info-label">版本标签</span>
                    <span class="info-value">v1.0.0</span>
                </div>
                <div class="info-item">
                    <span class="info-label">代码语言</span>
                    <span class="info-value">JavaScript (Node.js)</span>
                </div>
            </div>

            <!-- 配置信息 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background-color: #f39c12;">⚙️</div>
                    <h2 class="card-title">配置信息</h2>
                </div>
                <div class="config-item">
                    <div class="config-key">服务端口</div>
                    <div class="config-value">3000</div>
                </div>
                <div class="config-item">
                    <div class="config-key">上传目录</div>
                    <div class="config-value">./uploads</div>
                </div>
                <div class="config-item">
                    <div class="config-key">最大文件大小</div>
                    <div class="config-value">50MB</div>
                </div>
                <div class="config-item">
                    <div class="config-key">PDF渲染缩放</div>
                    <div class="config-value">2.0</div>
                </div>
                <div class="config-item">
                    <div class="config-key">环境变量</div>
                    <div class="config-value">NODE_ENV=production</div>
                </div>
            </div>
        </div>

        <!-- 第三行：服务信息和依赖信息 -->
        <div class="grid">
            <!-- 服务信息 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background-color: #27ae60;">🚀</div>
                    <h2 class="card-title">服务信息</h2>
                </div>
                <div class="metric">
                    <span class="metric-value">99.9%</span>
                    <div class="metric-label">服务可用性</div>
                </div>
                <div class="info-item">
                    <span class="info-label">运行状态</span>
                    <span class="info-value">
                        <span class="status running">运行中</span>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">启动时间</span>
                    <span class="info-value">2024-01-15 09:00:00</span>
                </div>
                <div class="info-item">
                    <span class="info-label">运行时长</span>
                    <span class="info-value">72小时 15分钟</span>
                </div>
                <div class="info-item">
                    <span class="info-label">内存使用</span>
                    <span class="info-value">256MB / 512MB</span>
                </div>
                <div class="info-item">
                    <span class="info-label">CPU使用率</span>
                    <span class="info-value">15%</span>
                </div>
            </div>

            <!-- 依赖信息 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon" style="background-color: #34495e;">📦</div>
                    <h2 class="card-title">依赖信息</h2>
                </div>
                <ul class="dependency-list">
                    <li class="dependency-item">
                        <span class="dependency-name">express</span>
                        <span class="dependency-version">^4.18.2</span>
                    </li>
                    <li class="dependency-item">
                        <span class="dependency-name">multer</span>
                        <span class="dependency-version">^1.4.5-lts.1</span>
                    </li>
                    <li class="dependency-item">
                        <span class="dependency-name">canvas</span>
                        <span class="dependency-version">^3.1.0</span>
                    </li>
                    <li class="dependency-item">
                        <span class="dependency-name">pdf-lib</span>
                        <span class="dependency-version">^1.17.1</span>
                    </li>
                    <li class="dependency-item">
                        <span class="dependency-name">pdfjs-dist</span>
                        <span class="dependency-version">^5.3.31</span>
                    </li>
                    <li class="dependency-item">
                        <span class="dependency-name">pdf.js-extract</span>
                        <span class="dependency-version">^0.2.1</span>
                    </li>
                    <li class="dependency-item">
                        <span class="dependency-name">dommatrix</span>
                        <span class="dependency-version">^0.1.1</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 添加一些交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为卡片添加点击效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // 实时更新运行时长（示例）
            function updateUptime() {
                const uptimeElement = document.querySelector('.info-item:nth-child(4) .info-value');
                if (uptimeElement && uptimeElement.textContent.includes('小时')) {
                    const currentTime = new Date();
                    const startTime = new Date('2024-01-15 09:00:00');
                    const diff = currentTime - startTime;
                    const hours = Math.floor(diff / (1000 * 60 * 60));
                    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                    uptimeElement.textContent = `${hours}小时 ${minutes}分钟`;
                }
            }

            // 每分钟更新一次运行时长
            setInterval(updateUptime, 60000);
        });
    </script>
</body>
</html>
