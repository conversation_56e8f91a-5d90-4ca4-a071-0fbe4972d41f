# PDF 分割工具

这是一个基于 Web 的 PDF 分割工具，可以将包含中间竖线的横版 PDF 试卷分割成两页。

## 功能特点

- 支持拖拽或点击上传 PDF 文件
- 实时预览 PDF 内容
- 自动识别中间竖线并分割
- 下载分割后的 PDF 文件

## 安装说明

1. 确保已安装 Node.js（建议版本 14.0.0 或更高）

2. 克隆项目并安装依赖：
```bash
npm install
```

3. 启动服务器：
```bash
npm start
```

4. 在浏览器中访问：http://localhost:3000

## 使用说明

1. 打开网页后，将 PDF 文件拖拽到指定区域或点击上传
2. 等待文件加载完成
3. 点击"分割并下载"按钮
4. 保存分割后的 PDF 文件

## 技术栈

- 前端：HTML5, JavaScript, PDF.js
- 后端：Node.js, Express
- PDF 处理：pdf-lib, pdf.js-extract 